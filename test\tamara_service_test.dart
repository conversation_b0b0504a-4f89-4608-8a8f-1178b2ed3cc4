import 'package:flutter_test/flutter_test.dart';
import 'package:webview_test/services/tamara_service.dart';
import 'package:webview_test/models/tamara_payment_request.dart';

void main() {
  group('TamaraService Tests', () {
    test('TamaraPaymentRequest should create valid JSON', () {
      final request = TamaraPaymentRequest(
        paymentMethod: 'tamara',
        reservationGuid: 'test-guid-123',
        deviceToken: 'test-token-456',
      );

      final json = request.toJson();

      expect(json['paymentMethod'], equals('tamara'));
      expect(json['reservationGuid'], equals('test-guid-123'));
      expect(json['deviceToken'], equals('test-token-456'));
    });

    test('TamaraPaymentRequest should create from JSON', () {
      final json = {
        'paymentMethod': 'tamara',
        'reservationGuid': 'test-guid-123',
        'deviceToken': 'test-token-456',
      };

      final request = TamaraPaymentRequest.fromJson(json);

      expect(request.paymentMethod, equals('tamara'));
      expect(request.reservationGuid, equals('test-guid-123'));
      expect(request.deviceToken, equals('test-token-456'));
    });

    test('isPaymentCallback should detect callback URLs', () {
      expect(TamaraService.isPaymentCallback('https://example.com/payment-success'), isTrue);
      expect(TamaraService.isPaymentCallback('https://example.com/payment-failure'), isTrue);
      expect(TamaraService.isPaymentCallback('https://example.com/payment-cancel'), isTrue);
      expect(TamaraService.isPaymentCallback('https://example.com/other-page'), isFalse);
    });

    test('getPaymentResult should return correct results', () {
      expect(TamaraService.getPaymentResult('https://example.com/payment-success'), 
             equals(PaymentResult.success));
      expect(TamaraService.getPaymentResult('https://example.com/payment-failure'), 
             equals(PaymentResult.failure));
      expect(TamaraService.getPaymentResult('https://example.com/payment-cancel'), 
             equals(PaymentResult.cancelled));
      expect(TamaraService.getPaymentResult('https://example.com/unknown'), 
             equals(PaymentResult.unknown));
    });
  });
}
