class TamaraPaymentResponse {
  final bool success;
  final String? paymentUrl;
  final String? sessionId;
  final String? errorMessage;
  final Map<String, dynamic>? data;

  TamaraPaymentResponse({
    required this.success,
    this.paymentUrl,
    this.sessionId,
    this.errorMessage,
    this.data,
  });

  factory TamaraPaymentResponse.fromJson(Map<String, dynamic> json) {
    return TamaraPaymentResponse(
      success: json['success'] ?? false,
      paymentUrl: json['paymentUrl'],
      sessionId: json['sessionId'],
      errorMessage: json['errorMessage'],
      data: json['data'],
    );
  }

  factory TamaraPaymentResponse.error(String message) {
    return TamaraPaymentResponse(
      success: false,
      errorMessage: message,
    );
  }
}
