import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/tamara_payment_request.dart';
import '../models/tamara_payment_response.dart';

class TamaraService {
  static const String baseUrl = 'https://takeed.runasp.net/api/v1/tamara';
  static const String createSessionEndpoint = '/create-session';

  /// Creates a payment session with Tamara
  /// Returns a TamaraPaymentResponse with payment URL or error
  static Future<TamaraPaymentResponse> createPaymentSession({
    required String reservationGuid,
    required String deviceToken,
  }) async {
    try {
      final request = TamaraPaymentRequest(
        paymentMethod: 'tamara',
        reservationGuid: reservationGuid,
        deviceToken: deviceToken,
      );

      final response = await http.post(
        Uri.parse('$baseUrl$createSessionEndpoint'),
        headers: {
          'accept': '*/*',
          'Content-Type': 'application/json',
        },
        body: jsonEncode(request.toJson()),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        
        // Assuming the API returns a payment URL in the response
        // You might need to adjust this based on the actual API response structure
        return TamaraPaymentResponse(
          success: true,
          paymentUrl: responseData['paymentUrl'] ?? responseData['url'],
          sessionId: responseData['sessionId'] ?? responseData['id'],
          data: responseData,
        );
      } else {
        return TamaraPaymentResponse.error(
          'Failed to create payment session. Status: ${response.statusCode}',
        );
      }
    } catch (e) {
      return TamaraPaymentResponse.error(
        'Error creating payment session: $e',
      );
    }
  }

  /// Validates payment callback URL
  static bool isPaymentCallback(String url) {
    // Add your callback URL patterns here
    return url.contains('payment-success') || 
           url.contains('payment-failure') || 
           url.contains('payment-cancel');
  }

  /// Extracts payment result from callback URL
  static PaymentResult getPaymentResult(String url) {
    if (url.contains('payment-success') || url.contains('success')) {
      return PaymentResult.success;
    } else if (url.contains('payment-failure') || url.contains('failed')) {
      return PaymentResult.failure;
    } else if (url.contains('payment-cancel') || url.contains('cancel')) {
      return PaymentResult.cancelled;
    }
    return PaymentResult.unknown;
  }
}

enum PaymentResult {
  success,
  failure,
  cancelled,
  unknown,
}
